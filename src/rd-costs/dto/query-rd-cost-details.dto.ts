import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';

export enum SortBy {
  CREATE_DATE = 'createDate',
  AMOUNT = 'amount',
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class QueryRdCostDetailsDto {
  @ApiProperty({
    description: '页码',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: '每页数量',
    example: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  pageSize?: number = 10;

  @ApiProperty({
    description: '起始日期',
    example: '2023-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsString()
  startTime?: string;

  @ApiProperty({
    description: '终止日期',
    example: '2023-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsString()
  endTime?: string;

  @ApiProperty({
    description: '搜索关键词（备注）',
    example: '样衣',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: '排序字段',
    enum: SortBy,
    example: SortBy.CREATE_DATE,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortBy, { message: '排序字段无效' })
  sortBy?: SortBy = SortBy.CREATE_DATE;

  @ApiProperty({
    description: '排序方向',
    enum: SortOrder,
    example: SortOrder.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortOrder, { message: '排序方向无效' })
  sortOrder?: SortOrder = SortOrder.DESC;

  @ApiProperty({
    description: '公司编码筛选',
    example: '01',
    required: false,
  })
  @IsOptional()
  @IsString()
  companyCode?: string;
}

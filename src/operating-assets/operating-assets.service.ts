import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import * as ExcelJS from 'exceljs';
import axios from 'axios';
import { OperatingAsset } from './entities/operating-asset.entity';
import {
  OperatingAssetDetail,
  OperatingAssetType,
  HumanResourcesAuditStatus,
  WarehouseLogisticsType,
} from './entities/operating-asset-detail.entity';
import { CreateOperatingAssetDetailDto } from './dto/create-operating-asset-detail.dto';
import { UpdateOperatingAssetDetailDto } from './dto/update-operating-asset-detail.dto';
import { QueryOperatingAssetDetailsDto } from './dto/query-operating-asset-details.dto';
import { CompaniesService } from '../companies/companies.service';

// 内部处理接口
interface ProcessedExportDto {
  startTime?: string;
  endTime?: string;
  detailIds?: string[];
  type?: OperatingAssetType;
  warehouseLogisticsType?: WarehouseLogisticsType;
  companyCode?: string;
}

// 运营资产类型中文映射
const OPERATING_ASSET_TYPE_LABELS = {
  [OperatingAssetType.HUMAN_RESOURCES]: '人力资产',
  [OperatingAssetType.WAREHOUSE_LOGISTICS]: '仓储物流',
  [OperatingAssetType.ADMINISTRATIVE_CONSUMPTION]: '管理费用',
  [OperatingAssetType.OTHER]: '招待费用',
};

// 人力资产审核状态中文映射
const HUMAN_RESOURCES_AUDIT_STATUS_LABELS = {
  [HumanResourcesAuditStatus.PENDING]: '未审核',
  [HumanResourcesAuditStatus.APPROVED]: '已审核',
};

// 仓储物流收支类型中文映射
const WAREHOUSE_LOGISTICS_TYPE_LABELS = {
  [WarehouseLogisticsType.INCOME]: '收入',
  [WarehouseLogisticsType.EXPENSE]: '支出',
};

@Injectable()
export class OperatingAssetsService {
  private readonly logger = new Logger(OperatingAssetsService.name);

  constructor(
    @InjectRepository(OperatingAsset)
    private readonly operatingAssetRepository: Repository<OperatingAsset>,
    @InjectRepository(OperatingAssetDetail)
    private readonly operatingAssetDetailRepository: Repository<OperatingAssetDetail>,
    private readonly dataSource: DataSource,
    private readonly companiesService: CompaniesService,
  ) {}

  // 获取或创建运营资产记录（系统只有一条记录）
  private async getOrCreateOperatingAsset(): Promise<OperatingAsset> {
    let operatingAsset = await this.operatingAssetRepository.findOne({
      where: { isDeleted: false },
    });

    if (!operatingAsset) {
      operatingAsset = this.operatingAssetRepository.create({
        totalAmount: 0,
        createDate: new Date().toISOString().split('T')[0],
      });
      await this.operatingAssetRepository.save(operatingAsset);
      this.logger.log('Created new operating asset record');
    }

    return operatingAsset;
  }

  // 重新计算并更新系统总金额
  private async updateSystemTotalAmount(
    queryRunner: any,
    operatingAssetId: string,
  ): Promise<void> {
    // 获取所有未删除的明细记录
    const allDetails = await queryRunner.manager.find(OperatingAssetDetail, {
      where: { operatingAssetId, isDeleted: false },
    });

    let totalAmount = 0;

    // 按类型分组计算
    const detailsByType = allDetails.reduce(
      (
        acc: Record<OperatingAssetType, OperatingAssetDetail[]>,
        detail: OperatingAssetDetail,
      ) => {
        if (!acc[detail.type]) {
          acc[detail.type] = [];
        }
        acc[detail.type].push(detail);
        return acc;
      },
      {} as Record<OperatingAssetType, OperatingAssetDetail[]>,
    );

    // 计算各类型的金额
    for (const [type, details] of Object.entries(detailsByType)) {
      if (type === OperatingAssetType.WAREHOUSE_LOGISTICS) {
        // 仓储物流：支出 - 收入
        let income = 0;
        let expense = 0;
        (details as OperatingAssetDetail[]).forEach(
          (detail: OperatingAssetDetail) => {
            if (
              detail.warehouseLogisticsType === WarehouseLogisticsType.INCOME
            ) {
              income += detail.amount;
            } else if (
              detail.warehouseLogisticsType === WarehouseLogisticsType.EXPENSE
            ) {
              expense += detail.amount;
            }
          },
        );
        totalAmount += expense - income;
      } else {
        // 其他类型：直接累加
        totalAmount += (details as OperatingAssetDetail[]).reduce(
          (sum: number, detail: OperatingAssetDetail) => sum + detail.amount,
          0,
        );
      }
    }

    // 更新系统总金额
    await queryRunner.manager.update(
      OperatingAsset,
      { id: operatingAssetId },
      { totalAmount },
    );
  }

  // 验证公司是否存在
  private async validateCompany(
    companyCode: string,
  ): Promise<{ code: string; name: string }> {
    if (!companyCode || companyCode.trim() === '') {
      throw new BadRequestException('公司编码不能为空');
    }

    const company = await this.companiesService.findByCode(companyCode);
    if (!company) {
      throw new BadRequestException(`公司 ${companyCode} 不存在`);
    }

    return { code: company.code, name: company.name };
  }

  // 新增运营资产明细
  async createDetail(
    createDetailDto: CreateOperatingAssetDetailDto,
  ): Promise<void> {
    const {
      companyCode,
      type,
      amount,
      screenshot,
      remark,
      humanResourcesAuditStatus,
      employeeInfo,
      warehouseLogisticsType,
      createDate,
    } = createDetailDto;

    // 验证公司
    const company = await this.validateCompany(companyCode);

    this.logger.log(
      `Creating operating asset detail with type: ${type}, amount: ${amount}`,
    );

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 获取或创建运营资产记录
      const operatingAsset = await this.getOrCreateOperatingAsset();

      // 如果没有提供 createDate，使用当前日期
      const finalCreateDate =
        createDate || new Date().toISOString().split('T')[0];

      // 创建明细记录
      const detail = queryRunner.manager.create(OperatingAssetDetail, {
        operatingAssetId: operatingAsset.id,
        companyCode: company.code,
        type,
        amount,
        screenshot,
        remark,
        createDate: finalCreateDate,
        // 只有人力资产类型才设置这些字段
        humanResourcesAuditStatus:
          type === OperatingAssetType.HUMAN_RESOURCES
            ? humanResourcesAuditStatus
            : null,
        employeeInfo:
          type === OperatingAssetType.HUMAN_RESOURCES ? employeeInfo : null,
        // 只有仓储物流类型才设置收支类型字段
        warehouseLogisticsType:
          type === OperatingAssetType.WAREHOUSE_LOGISTICS
            ? warehouseLogisticsType
            : null,
      });

      await queryRunner.manager.save(detail);

      // 更新总金额 - 仓储物流需要特殊处理
      await this.updateSystemTotalAmount(queryRunner, operatingAsset.id);

      await queryRunner.commitTransaction();
      this.logger.log(
        `Operating asset detail created successfully with type: ${type}, amount: ${amount}`,
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 分页查询运营资产明细
  async findAllDetails(queryDto: QueryOperatingAssetDetailsDto) {
    const {
      page = 1,
      pageSize = 10,
      type,
      warehouseLogisticsType,
      startTime,
      endTime,
      search,
      sortBy = 'createDate',
      sortOrder = 'DESC',
      companyCode,
    } = queryDto;

    this.logger.log(
      `Querying operating asset details: page=${page}, pageSize=${pageSize}, type=${type}, warehouseLogisticsType=${warehouseLogisticsType}`,
    );

    // 获取运营资产总金额
    const operatingAsset = await this.getOrCreateOperatingAsset();

    // 处理时间参数
    let processedStartTime = startTime;
    let processedEndTime = endTime;

    if (startTime) {
      // 如果是日期格式（如 2025-05-01），转换为当天开始时间
      if (startTime.length === 10) {
        processedStartTime = `${startTime}T00:00:00.000Z`;
      }
    }

    if (endTime) {
      // 如果是日期格式（如 2025-05-31），转换为当天结束时间
      if (endTime.length === 10) {
        processedEndTime = `${endTime}T23:59:59.999Z`;
      }
    }

    // 构建查询条件
    const queryBuilder = this.operatingAssetDetailRepository
      .createQueryBuilder('detail')
      .leftJoin('companies', 'company', 'company.code = detail.companyCode')
      .addSelect('company.name', 'companyName')
      .where('detail.isDeleted = :isDeleted', { isDeleted: false });

    // 类型筛选
    if (type) {
      queryBuilder.andWhere('detail.type = :type', { type });
    }

    // 仓储物流收支类型筛选
    if (warehouseLogisticsType) {
      queryBuilder.andWhere(
        'detail.warehouselogisticstype = :warehouseLogisticsType',
        {
          warehouseLogisticsType,
        },
      );
    }

    // 时间范围筛选 - 由于createDate是字符串类型，使用CAST转换为日期进行比较
    if (processedStartTime) {
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) >= CAST(:startTime AS DATE)',
        {
          startTime: processedStartTime,
        },
      );
    }

    if (processedEndTime) {
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) <= CAST(:endTime AS DATE)',
        {
          endTime: processedEndTime,
        },
      );
    }

    // 搜索条件（备注模糊搜索）
    if (search) {
      queryBuilder.andWhere('detail.remark ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // 公司编码筛选
    if (companyCode) {
      queryBuilder.andWhere('detail.companyCode = :companyCode', {
        companyCode,
      });
    }

    // 排序
    queryBuilder.orderBy(`detail.${sortBy}`, sortOrder as 'ASC' | 'DESC');

    // 处理分页参数
    if (page === 0 && pageSize === 0) {
      // 返回所有数据
      const result = await queryBuilder.getRawAndEntities();
      const details = result.entities.map((detail, index) => ({
        ...detail,
        companyName: result.raw[index]?.companyName || null,
      }));

      // 为每个明细添加类型中文名称和审核状态中文名称
      const detailsWithTypeLabel = details.map((detail) => ({
        ...detail,
        typeLabel: OPERATING_ASSET_TYPE_LABELS[detail.type],
        // 只有人力资产类型才显示审核状态标签
        humanResourcesAuditStatusLabel:
          detail.type === OperatingAssetType.HUMAN_RESOURCES &&
          detail.humanResourcesAuditStatus
            ? HUMAN_RESOURCES_AUDIT_STATUS_LABELS[
                detail.humanResourcesAuditStatus
              ]
            : null,
        // 只有仓储物流类型才显示收支类型标签
        warehouseLogisticsTypeLabel:
          detail.type === OperatingAssetType.WAREHOUSE_LOGISTICS &&
          detail.warehouseLogisticsType
            ? WAREHOUSE_LOGISTICS_TYPE_LABELS[detail.warehouseLogisticsType]
            : null,
      }));

      // 计算查询结果的总金额 - 按类型正确计算
      let queryResultTotalAmount = 0;
      if (type === OperatingAssetType.WAREHOUSE_LOGISTICS) {
        // 仓储物流：支出 - 收入
        let income = 0;
        let expense = 0;
        details.forEach((detail) => {
          if (detail.warehouseLogisticsType === WarehouseLogisticsType.INCOME) {
            income += detail.amount;
          } else if (
            detail.warehouseLogisticsType === WarehouseLogisticsType.EXPENSE
          ) {
            expense += detail.amount;
          }
        });
        queryResultTotalAmount = expense - income;
      } else if (type) {
        // 其他特定类型：直接求和
        queryResultTotalAmount = details.reduce(
          (sum, detail) => sum + detail.amount,
          0,
        );
      } else {
        // 全部类型：按类型分别计算
        const detailsByType = details.reduce(
          (acc: Record<OperatingAssetType, typeof details>, detail) => {
            if (!acc[detail.type]) {
              acc[detail.type] = [];
            }
            acc[detail.type].push(detail);
            return acc;
          },
          {} as Record<OperatingAssetType, typeof details>,
        );

        for (const [detailType, typeDetails] of Object.entries(detailsByType)) {
          if (detailType === OperatingAssetType.WAREHOUSE_LOGISTICS) {
            // 仓储物流：支出 - 收入
            let warehouseIncome = 0;
            let warehouseExpense = 0;
            typeDetails.forEach((detail) => {
              if (
                detail.warehouseLogisticsType === WarehouseLogisticsType.INCOME
              ) {
                warehouseIncome += detail.amount;
              } else if (
                detail.warehouseLogisticsType === WarehouseLogisticsType.EXPENSE
              ) {
                warehouseExpense += detail.amount;
              }
            });
            queryResultTotalAmount += warehouseExpense - warehouseIncome;
          } else {
            // 其他类型：直接累加
            queryResultTotalAmount += typeDetails.reduce(
              (sum, detail) => sum + detail.amount,
              0,
            );
          }
        }
      }

      return {
        details: detailsWithTypeLabel,
        total: details.length,
        page: 0,
        pageSize: 0,
        totalAmount: queryResultTotalAmount,
        systemTotalAmount: operatingAsset.totalAmount,
        startTime: processedStartTime || null,
        endTime: processedEndTime || null,
      };
    }

    // 正常分页查询
    const paginatedResult = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getRawAndEntities();

    const details = paginatedResult.entities.map((detail, index) => ({
      ...detail,
      companyName: paginatedResult.raw[index]?.companyName || null,
    }));

    // 获取总数
    const total = await queryBuilder.getCount();

    // 计算所有查询条件下的总金额（不分页）
    const allMatchingResult = await queryBuilder
      .skip(0)
      .take(undefined)
      .getRawAndEntities();

    const allMatchingDetails = allMatchingResult.entities;

    // 计算总金额逻辑优化：
    let queryResultTotalAmount = 0;
    let income = 0;
    let expense = 0;

    if (type === OperatingAssetType.WAREHOUSE_LOGISTICS) {
      // 只查询仓储物流时，统计收入/支出
      allMatchingDetails.forEach((detail) => {
        if (detail.warehouseLogisticsType === WarehouseLogisticsType.INCOME) {
          income += detail.amount;
        } else if (
          detail.warehouseLogisticsType === WarehouseLogisticsType.EXPENSE
        ) {
          expense += detail.amount;
        }
      });
      queryResultTotalAmount = expense - income;
    } else if (type) {
      // 查询其他特定类型时，直接求和
      queryResultTotalAmount = allMatchingDetails.reduce(
        (sum, detail) => sum + detail.amount,
        0,
      );
      income = 0;
      expense = 0;
    } else {
      // 查询全部类型时，按类型分别计算后求和
      const detailsByType = allMatchingDetails.reduce(
        (
          acc: Record<OperatingAssetType, typeof allMatchingDetails>,
          detail,
        ) => {
          if (!acc[detail.type]) {
            acc[detail.type] = [];
          }
          acc[detail.type].push(detail);
          return acc;
        },
        {} as Record<OperatingAssetType, typeof allMatchingDetails>,
      );

      for (const [detailType, details] of Object.entries(detailsByType)) {
        if (detailType === OperatingAssetType.WAREHOUSE_LOGISTICS) {
          // 仓储物流：支出 - 收入
          let warehouseIncome = 0;
          let warehouseExpense = 0;
          details.forEach((detail) => {
            if (
              detail.warehouseLogisticsType === WarehouseLogisticsType.INCOME
            ) {
              warehouseIncome += detail.amount;
            } else if (
              detail.warehouseLogisticsType === WarehouseLogisticsType.EXPENSE
            ) {
              warehouseExpense += detail.amount;
            }
          });
          queryResultTotalAmount += warehouseExpense - warehouseIncome;
        } else {
          // 其他类型：直接累加
          queryResultTotalAmount += details.reduce(
            (sum, detail) => sum + detail.amount,
            0,
          );
        }
      }
      income = 0;
      expense = 0;
    }

    // 为每个明细添加类型中文名称和审核状态中文名称
    const detailsWithTypeLabel = details.map((detail) => ({
      ...detail,
      typeLabel: OPERATING_ASSET_TYPE_LABELS[detail.type],
      // 只有人力资产类型才显示审核状态标签
      humanResourcesAuditStatusLabel:
        detail.type === OperatingAssetType.HUMAN_RESOURCES &&
        detail.humanResourcesAuditStatus
          ? HUMAN_RESOURCES_AUDIT_STATUS_LABELS[
              detail.humanResourcesAuditStatus
            ]
          : null,
      // 只有仓储物流类型才显示收支类型标签
      warehouseLogisticsTypeLabel:
        detail.type === OperatingAssetType.WAREHOUSE_LOGISTICS &&
        detail.warehouseLogisticsType
          ? WAREHOUSE_LOGISTICS_TYPE_LABELS[detail.warehouseLogisticsType]
          : null,
    }));

    // 返回结果时，type为仓储物流时，返回income/expense字段，否则不返回
    const result: any = {
      details: detailsWithTypeLabel,
      total,
      page,
      pageSize,
      totalAmount: queryResultTotalAmount,
      systemTotalAmount: operatingAsset.totalAmount,
      startTime: processedStartTime || null,
      endTime: processedEndTime || null,
    };
    if (type === OperatingAssetType.WAREHOUSE_LOGISTICS) {
      result.income = income;
      result.expense = expense;
    }
    return result;
  }

  // 获取运营资产明细详情
  async findDetailById(id: string): Promise<
    OperatingAssetDetail & {
      typeLabel: string;
      humanResourcesAuditStatusLabel?: string;
      warehouseLogisticsTypeLabel?: string;
    }
  > {
    this.logger.log(`Finding operating asset detail by id: ${id}`);

    const detail = await this.operatingAssetDetailRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!detail) {
      throw new NotFoundException('运营资产明细不存在');
    }

    return {
      ...detail,
      typeLabel: OPERATING_ASSET_TYPE_LABELS[detail.type],
      // 只有人力资产类型才显示审核状态标签
      humanResourcesAuditStatusLabel:
        detail.type === OperatingAssetType.HUMAN_RESOURCES &&
        detail.humanResourcesAuditStatus
          ? HUMAN_RESOURCES_AUDIT_STATUS_LABELS[
              detail.humanResourcesAuditStatus
            ]
          : undefined,
      // 只有仓储物流类型才显示收支类型标签
      warehouseLogisticsTypeLabel:
        detail.type === OperatingAssetType.WAREHOUSE_LOGISTICS &&
        detail.warehouseLogisticsType
          ? WAREHOUSE_LOGISTICS_TYPE_LABELS[detail.warehouseLogisticsType]
          : undefined,
    };
  }

  // 更新运营资产明细
  async updateDetail(
    id: string,
    updateDetailDto: UpdateOperatingAssetDetailDto,
  ): Promise<void> {
    this.logger.log(`Updating operating asset detail: ${id}`);

    // 如果更新了公司编码，需要验证
    if (updateDetailDto.companyCode) {
      await this.validateCompany(updateDetailDto.companyCode);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 查找现有明细
      const existingDetail = await queryRunner.manager.findOne(
        OperatingAssetDetail,
        {
          where: { id, isDeleted: false },
        },
      );

      if (!existingDetail) {
        throw new NotFoundException('运营资产明细不存在');
      }

      const oldAmount = existingDetail.amount;
      const newAmount = updateDetailDto.amount ?? oldAmount;
      const amountDifference = newAmount - oldAmount;

      // 更新明细记录
      await queryRunner.manager.update(
        OperatingAssetDetail,
        { id },
        updateDetailDto,
      );

      // 如果金额有变化或类型有变化，重新计算总金额
      if (
        amountDifference !== 0 ||
        updateDetailDto.warehouseLogisticsType !== undefined
      ) {
        const operatingAsset = await this.getOrCreateOperatingAsset();
        await this.updateSystemTotalAmount(queryRunner, operatingAsset.id);
      }

      await queryRunner.commitTransaction();
      this.logger.log(`Operating asset detail updated successfully: ${id}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 删除运营资产明细（软删除）
  async removeDetail(id: string): Promise<void> {
    this.logger.log(`Removing operating asset detail: ${id}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 查找现有明细
      const existingDetail = await queryRunner.manager.findOne(
        OperatingAssetDetail,
        {
          where: { id, isDeleted: false },
        },
      );

      if (!existingDetail) {
        throw new NotFoundException('运营资产明细不存在');
      }

      // 软删除明细记录
      await queryRunner.manager.update(
        OperatingAssetDetail,
        { id },
        {
          isDeleted: true,
        },
      );

      // 重新计算总金额
      const operatingAsset = await this.getOrCreateOperatingAsset();
      await this.updateSystemTotalAmount(queryRunner, operatingAsset.id);

      await queryRunner.commitTransaction();
      this.logger.log(`Operating asset detail removed successfully: ${id}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 导出Excel
  async exportToExcel(exportDto?: ProcessedExportDto): Promise<Buffer> {
    this.logger.log('Exporting operating asset details to Excel');

    // 验证：如果没有提供明细ID，必须提供时间范围
    if (!exportDto?.detailIds || exportDto.detailIds.length === 0) {
      if (!exportDto?.startTime || !exportDto?.endTime) {
        throw new BadRequestException(
          '请选择明细进行导出，或提供起始时间和终止时间进行时间范围导出',
        );
      }
    }

    // 获取运营资产记录
    const operatingAsset = await this.getOrCreateOperatingAsset();

    // 构建查询条件
    const queryBuilder = this.operatingAssetDetailRepository
      .createQueryBuilder('detail')
      .leftJoin('companies', 'company', 'company.code = detail.companyCode')
      .where('detail.isDeleted = false');

    // 时间范围筛选 - 由于createDate是字符串类型，使用CAST转换为日期进行比较
    if (exportDto?.startTime) {
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) >= CAST(:startTime AS DATE)',
        {
          startTime: exportDto.startTime,
        },
      );
    }

    if (exportDto?.endTime) {
      queryBuilder.andWhere(
        'CAST(detail.createDate AS DATE) <= CAST(:endTime AS DATE)',
        {
          endTime: exportDto.endTime,
        },
      );
    }

    // 类型筛选
    if (exportDto?.type) {
      queryBuilder.andWhere('detail.type = :type', { type: exportDto.type });
    }

    // 仓储物流收支类型筛选
    if (exportDto?.warehouseLogisticsType) {
      queryBuilder.andWhere(
        'detail.warehouselogisticstype = :warehouseLogisticsType',
        {
          warehouseLogisticsType: exportDto.warehouseLogisticsType,
        },
      );
    }

    // 明细ID筛选
    if (exportDto?.detailIds && exportDto.detailIds.length > 0) {
      queryBuilder.andWhere('detail.id IN (:...detailIds)', {
        detailIds: exportDto.detailIds,
      });
    }

    // 公司编码筛选
    if (exportDto?.companyCode) {
      queryBuilder.andWhere('detail.companyCode = :companyCode', {
        companyCode: exportDto.companyCode,
      });
    }

    // 排序
    queryBuilder.orderBy('detail.createDate', 'DESC');

    const details = await queryBuilder.getMany();

    this.logger.log(`Found ${details.length} details for export`);
    if (details.length > 0) {
      this.logger.log(
        `First detail ID: ${details[0].id}, Type: ${details[0].type}, Amount: ${details[0].amount}`,
      );
    }

    // 计算选中明细的总金额
    const selectedTotalAmount = details.reduce(
      (sum, detail) => sum + detail.amount,
      0,
    );

    // 生成Excel（支持图片插入）
    try {
      // 创建工作簿
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('运营资产明细');

      // 检查是否包含人力资产类型
      const hasHumanResources = details.some(
        (detail) => detail.type === OperatingAssetType.HUMAN_RESOURCES,
      );

      // 检查是否包含仓储物流类型
      const hasWarehouseLogistics = details.some(
        (detail) => detail.type === OperatingAssetType.WAREHOUSE_LOGISTICS,
      );

      // 根据包含的类型设置列宽
      const columns = [
        { header: '序号', key: 'index', width: 8 },
        { header: '类型', key: 'type', width: 15 },
        { header: '金额', key: 'amount', width: 15 },
      ];

      if (hasHumanResources) {
        columns.push(
          { header: '审核状态', key: 'auditStatus', width: 12 },
          { header: '员工信息', key: 'employeeInfo', width: 20 },
        );
      }

      if (hasWarehouseLogistics) {
        columns.push({ header: '收支类型', key: 'warehouseType', width: 12 });
      }

      columns.push(
        { header: '创建时间', key: 'createdAt', width: 20 },
        { header: '截图', key: 'screenshot', width: 30 },
        { header: '备注', key: 'remark', width: 30 },
      );

      worksheet.columns = columns;

      // 添加标题
      const totalColumns = columns.length;
      const titleColSpan = `A1:${String.fromCharCode(64 + totalColumns)}1`;
      worksheet.mergeCells(titleColSpan);
      const titleCell = worksheet.getCell('A1');
      titleCell.value = '运营资产管理报告';
      titleCell.font = { bold: true, size: 16 };
      titleCell.alignment = { horizontal: 'center' };

      // 添加概况信息
      let currentRow = 3;
      worksheet.getCell(`A${currentRow}`).value = '资产概况';
      worksheet.getCell(`A${currentRow}`).font = { bold: true, size: 14 };
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '运营资产总金额';
      worksheet.getCell(`B${currentRow}`).value =
        `¥${operatingAsset.totalAmount.toFixed(2)}`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '导出明细总金额';
      worksheet.getCell(`B${currentRow}`).value =
        `¥${selectedTotalAmount.toFixed(2)}`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '导出明细数量';
      worksheet.getCell(`B${currentRow}`).value = `${details.length}条`;
      currentRow++;

      worksheet.getCell(`A${currentRow}`).value = '导出时间';
      worksheet.getCell(`B${currentRow}`).value = new Date().toLocaleString(
        'zh-CN',
      );
      currentRow += 2;

      // 添加明细记录标题
      worksheet.getCell(`A${currentRow}`).value = '明细记录';
      worksheet.getCell(`A${currentRow}`).font = { bold: true, size: 14 };
      currentRow++;

      // 添加表头
      const headerRow = worksheet.getRow(currentRow);
      const headerValues = ['序号', '类型', '金额'];

      if (hasHumanResources) {
        headerValues.push('审核状态', '员工信息');
      }

      if (hasWarehouseLogistics) {
        headerValues.push('收支类型');
      }

      headerValues.push('创建时间', '截图', '备注');
      headerRow.values = headerValues;
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };
      currentRow++;

      // 添加明细数据和图片
      for (let i = 0; i < details.length; i++) {
        const detail = details[i];
        const row = worksheet.getRow(currentRow);

        // 设置行高以容纳图片
        row.height = 120;

        // 添加基本数据
        const rowValues = [
          i + 1,
          OPERATING_ASSET_TYPE_LABELS[detail.type],
          `¥${detail.amount.toFixed(2)}`,
        ];

        if (hasHumanResources) {
          rowValues.push(
            detail.type === OperatingAssetType.HUMAN_RESOURCES &&
              detail.humanResourcesAuditStatus
              ? HUMAN_RESOURCES_AUDIT_STATUS_LABELS[
                  detail.humanResourcesAuditStatus
                ]
              : '',
            detail.type === OperatingAssetType.HUMAN_RESOURCES
              ? detail.employeeInfo || ''
              : '',
          );
        }

        if (hasWarehouseLogistics) {
          rowValues.push(
            detail.type === OperatingAssetType.WAREHOUSE_LOGISTICS &&
              detail.warehouseLogisticsType
              ? WAREHOUSE_LOGISTICS_TYPE_LABELS[detail.warehouseLogisticsType]
              : '',
          );
        }

        rowValues.push(
          detail.createDate,
          '', // 截图列留空，用于插入图片
          detail.remark || '',
        );

        row.values = rowValues;

        // 下载并插入图片
        try {
          if (detail.screenshot) {
            this.logger.log(`Downloading image: ${detail.screenshot}`);

            const imageResponse = await axios.get(detail.screenshot, {
              responseType: 'arraybuffer',
              timeout: 10000,
            });

            const imageBuffer = Buffer.from(imageResponse.data);

            // 添加图片到工作簿
            const imageId = workbook.addImage({
              buffer: imageBuffer,
              extension: 'jpeg',
            });

            // 在截图列插入图片
            const screenshotCol = hasHumanResources ? 6 : 4; // 根据是否有人力资产字段调整列位置
            worksheet.addImage(imageId, {
              tl: { col: screenshotCol, row: currentRow - 1 }, // 从截图列开始
              ext: { width: 200, height: 100 }, // 图片大小
            });

            this.logger.log(`Image inserted successfully for detail ${i + 1}`);
          }
        } catch (imageError) {
          this.logger.warn(
            `Failed to load image for detail ${i + 1}: ${detail.screenshot}`,
            imageError.message,
          );
          // 如果图片加载失败，在截图列显示链接
          const screenshotCellIndex = hasHumanResources ? 7 : 5;
          row.getCell(screenshotCellIndex).value = detail.screenshot;
        }

        currentRow++;
      }

      // 设置边框
      const borderStyle = {
        top: { style: 'thin' as const },
        left: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        right: { style: 'thin' as const },
      };

      // 为表格添加边框
      const tableStartRow = currentRow - details.length - 1;
      const tableEndRow = currentRow - 1;
      const maxCol = hasHumanResources ? 8 : 6;
      for (let row = tableStartRow; row <= tableEndRow; row++) {
        for (let col = 1; col <= maxCol; col++) {
          worksheet.getCell(row, col).border = borderStyle;
        }
      }

      // 生成Excel缓冲区
      const excelBuffer = await workbook.xlsx.writeBuffer();
      return Buffer.from(excelBuffer);
    } catch (error) {
      this.logger.error('Excel generation failed', error);
      throw new BadRequestException('Excel生成失败');
    }
  }
}
